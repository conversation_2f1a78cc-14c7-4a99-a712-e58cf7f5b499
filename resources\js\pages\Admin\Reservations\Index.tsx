import DataTable from '@/components/DataTable';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { UserInfo } from '@/components/user-info';
import AppLayout from '@/layouts/app-layout';
import { BreadcrumbItem, PageProps, PaginatedData, Reservation, Stage, TypeStage, User } from '@/types';
import { Head, router, useForm } from '@inertiajs/react';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Calendar, CreditCard, Hash } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import TabNavigation from '@/components/TabNavigation';

interface ReservationsPageProps extends PageProps {
  reservations: PaginatedData<Reservation>;
  stages: Stage[];
  typeStages: TypeStage[];
  users: User[];
  isArchive?: boolean;
}

export default function Index({ reservations, stages, typeStages, users, isArchive = false }: ReservationsPageProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isImportOpen, setIsImportOpen] = useState(false);
  const [editingReservation, setEditingReservation] = useState<Reservation | null>(null);

  const form = useForm({
    stage_id: '',
    user_id: '',
    type_stage_id: '',
    date_reservation: '',
    statut: 'en attente',
    date_infraction: '',
    heure_infraction: '',
    lieu_infraction: '',
    permis_recto: '',
    permis_verso: '',
    lettre_48n_recto: '',
    lettre_48n_verso: '',
    // Ajout des champs pour les fichiers
    permis_recto_file: null as File | null,
    permis_verso_file: null as File | null,
    lettre_48n_recto_file: null as File | null,
    lettre_48n_verso_file: null as File | null,
    // Ajout des champs de paiement
    date_paiement: '',
    methode_paiement: '',
    transaction_id: '',
  });

  const importForm = useForm({
    file: null as File | null,
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'confirmée':
        return <Badge className="bg-green-500">Confirmée</Badge>;
      case 'en attente':
        return <Badge className="bg-yellow-500">En attente</Badge>;
      case 'annulée':
        return <Badge className="bg-red-500">Annulée</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  const columns = [
    {
      key: 'user',
      label: 'Client',
      render: (_value: unknown, row: Record<string, unknown>) => {
        const reservation = row as unknown as Reservation;
        return reservation.user ? (
          <div className="flex items-center gap-2">
            <UserInfo user={reservation.user} showEmail={true} />
          </div>
        ) : (
          ''
        );
      },
    },
    {
      key: 'stage',
      label: 'Stage',
      render: (_value: unknown, row: Record<string, unknown>) => {
        const reservation = row as unknown as Reservation;
        return reservation.stage ? (
          <div className="flex flex-col">
            <span className="font-medium">{reservation.stage.reference}</span>
            <span className="text-xs text-gray-500">
              {reservation.stage.lieu?.nom} - {format(parseISO(reservation.stage.date_debut), 'dd/MM/yyyy', { locale: fr })}
            </span>
          </div>
        ) : (
          ''
        );
      },
    },
    {
      key: 'type_stage',
      index: 'type_stage_id',
      label: 'Type de stage',
      render: (_value: unknown, row: Record<string, unknown>) => {
        const reservation = row as unknown as Reservation;
        return reservation.type_stage?.nom || '';
      },
    },
    {
      key: 'date_reservation',
      label: 'Date de réservation',
      render: (value: unknown) => {
        if (typeof value === 'string') {
          return format(parseISO(value), 'dd/MM/yyyy HH:mm', { locale: fr });
        }
        return '';
      },
    },
    {
      key: 'statut',
      label: 'Statut',
      render: (value: unknown) => {
        return getStatusBadge(value as string);
      },
    },
    {
      key: 'payment_details',
      label: 'Détails de paiement',
      render: (_value: unknown, row: Record<string, unknown>) => {
        const reservation = row as unknown as Reservation;

        // Only show payment details if payment information exists
        if (!reservation.date_paiement && !reservation.methode_paiement && !reservation.transaction_id) {
          return <span className="text-muted-foreground text-sm">Aucun paiement</span>;
        }

        return (
          <div className="flex min-w-45 items-center gap-2">
            <div className="grid flex-1 text-left text-sm leading-tight">
              {reservation.methode_paiement && (
                <div className="flex items-center gap-1">
                  <CreditCard className="h-3 w-3" />
                  <span className="truncate font-medium">{reservation.methode_paiement}</span>
                </div>
              )}
              {reservation.date_paiement && (
                <div className="text-muted-foreground flex items-center gap-1 text-xs">
                  <Calendar className="h-3 w-3" />
                  <span className="truncate">{format(parseISO(reservation.date_paiement), 'dd/MM/yyyy HH:mm', { locale: fr })}</span>
                </div>
              )}
              {reservation.transaction_id && (
                <div className="text-muted-foreground flex items-center gap-1 text-xs">
                  <Hash className="h-3 w-3" />
                  <span className="truncate" title={reservation.transaction_id}>
                    {reservation.transaction_id.length > 15 ? `${reservation.transaction_id.substring(0, 15)}...` : reservation.transaction_id}
                  </span>
                </div>
              )}
            </div>
          </div>
        );
      },
    },
  ];

  const handleAdd = () => {
    setEditingReservation(null);
    form.reset();
    form.setData('statut', 'en attente');
    setIsOpen(true);
  };

  const handleImport = () => {
    importForm.reset();
    setIsImportOpen(true);
  };

  const handleImportSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    importForm.post(route('admin.reservations.import'), {
      forceFormData: true,
      onSuccess: () => {
        setIsImportOpen(false);
        // toast.success('Réservations importées avec succès');
      },
      onError: (errors) => {
        console.log(errors);
        toast.error("Erreur lors de l'importation : " + (errors.file || errors.message || 'Erreur inconnue'));
      },
    });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      importForm.setData('file', e.target.files[0]);
    }
  };

  const handleEdit = (row: Record<string, unknown>) => {
    const reservation = row as unknown as Reservation;
    setEditingReservation(reservation);

    // Vérifier que les IDs existent et les convertir en chaînes de caractères
    const stage_id = reservation.stage_id !== undefined ? reservation.stage_id.toString() : '';
    const user_id = reservation.user_id !== undefined ? reservation.user_id.toString() : '';
    const type_stage_id = reservation.type_stage_id !== undefined ? reservation.type_stage_id.toString() : '';

    // Formater les dates si elles existent
    const formattedDateReservation = reservation.date_reservation ? format(parseISO(reservation.date_reservation), 'yyyy-MM-dd') : '';

    const formattedDateInfraction = reservation.date_infraction ? format(parseISO(reservation.date_infraction), 'yyyy-MM-dd') : '';

    // Formater la date de paiement si elle existe
    const formattedDatePaiement = reservation.date_paiement ? format(parseISO(reservation.date_paiement), 'yyyy-MM-dd') : '';

    form.setData({
      stage_id: stage_id,
      user_id: user_id,
      type_stage_id: type_stage_id,
      date_reservation: formattedDateReservation,
      statut: reservation.statut || 'en attente',
      date_infraction: formattedDateInfraction,
      heure_infraction: reservation.heure_infraction || '',
      lieu_infraction: reservation.lieu_infraction || '',
      permis_recto: reservation.permis_recto || '',
      permis_verso: reservation.permis_verso || '',
      lettre_48n_recto: reservation.lettre_48n_recto || '',
      lettre_48n_verso: reservation.lettre_48n_verso || '',
      // Initialiser les champs de fichiers à null
      permis_recto_file: null,
      permis_verso_file: null,
      lettre_48n_recto_file: null,
      lettre_48n_verso_file: null,
      // Ajouter les champs de paiement
      date_paiement: formattedDatePaiement,
      methode_paiement: reservation.methode_paiement || '',
      transaction_id: reservation.transaction_id || '',
    });

    setIsOpen(true);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (editingReservation) {
      form.post(route('admin.reservations.update', editingReservation.id), {
        forceFormData: true,
        onSuccess: () => {
          setIsOpen(false);
        },
      });
    } else {
      form.post(route('admin.reservations.store'), {
        forceFormData: true,
        onSuccess: () => {
          setIsOpen(false);
        },
      });
    }
  };

  const handleDelete = (row: Record<string, unknown>) => {
    const reservation = row as unknown as Reservation;
    if (confirm('Êtes-vous sûr de vouloir supprimer cette réservation ?')) {
      router.delete(route('admin.reservations.destroy', reservation.id));
    }
  };

  const handleShow = (row: Record<string, unknown>) => {
    const reservation = row as unknown as Reservation;
    router.visit(route('admin.reservations.show', reservation.id));
  };

  const breadcrumbs: BreadcrumbItem[] = [
    {
      title: 'Réservations',
      href: '/admin/reservations',
    },
  ];

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title={isArchive ? "Archives des réservations" : "Réservations"} />
      <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
        <TabNavigation
          tabs={[
            {
              name: 'Réservations à venir',
              href: route('admin.reservations.index'),
              current: !isArchive,
            },
            {
              name: 'Archives',
              href: route('admin.reservations.archive'),
              current: isArchive,
            },
          ]}
        />

        <DataTable
          title={isArchive ? "Archives des réservations" : "Réservations"}
          columns={columns}
          data={reservations.data.map((reservation) => ({
            id: reservation.id,
            user: reservation.user,
            stage: reservation.stage,
            type_stage: reservation.type_stage,
            date_reservation: reservation.date_reservation,
            statut: reservation.statut,
            // Ajouter les IDs nécessaires pour l'édition
            stage_id: reservation.stage_id,
            user_id: reservation.user_id,
            type_stage_id: reservation.type_stage_id,
            // Ajouter les autres champs qui pourraient être nécessaires
            date_infraction: reservation.date_infraction,
            heure_infraction: reservation.heure_infraction,
            lieu_infraction: reservation.lieu_infraction,
            permis_recto: reservation.permis_recto,
            permis_verso: reservation.permis_verso,
            lettre_48n_recto: reservation.lettre_48n_recto,
            lettre_48n_verso: reservation.lettre_48n_verso,
            // Ajouter les champs de paiement
            date_paiement: reservation.date_paiement,
            methode_paiement: reservation.methode_paiement,
            transaction_id: reservation.transaction_id,
          }))}
          onAdd={!isArchive ? handleAdd : undefined}
          onImport={!isArchive ? handleImport : undefined}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onShow={handleShow}
          pagination={{
            links: reservations.links,
            from: reservations.from,
            to: reservations.to,
            total: reservations.total,
          }}
        />
      </div>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-[600px] md:max-w-[700px] lg:max-w-[900px]">
          <DialogHeader>
            <DialogTitle>{editingReservation ? 'Modifier la réservation' : 'Ajouter une réservation'}</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="mb-1 block text-sm font-medium">Client</label>
                <Select value={form.data.user_id} onValueChange={(value) => form.setData('user_id', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner un client" />
                  </SelectTrigger>
                  <SelectContent>
                    {users.map((user) => (
                      <SelectItem key={user.id} value={user.id.toString()}>
                        {user.nom} {user.prenom} - {user.email}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="mb-1 block text-sm font-medium">Stage</label>
                <Select value={form.data.stage_id} onValueChange={(value) => form.setData('stage_id', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner un stage" />
                  </SelectTrigger>
                  <SelectContent>
                    {stages.map((stage) => (
                      <SelectItem key={stage.id} value={stage.id.toString()}>
                        {stage.reference} - {stage.lieu?.nom} ({format(parseISO(stage.date_debut), 'dd/MM/yyyy', { locale: fr })})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="mb-1 block text-sm font-medium">Type de stage</label>
                <Select value={form.data.type_stage_id} onValueChange={(value) => form.setData('type_stage_id', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner un type de stage" />
                  </SelectTrigger>
                  <SelectContent>
                    {typeStages.map((type) => (
                      <SelectItem key={type.id} value={type.id.toString()}>
                        {type.nom}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="mb-1 block text-sm font-medium">Statut</label>
                <Select value={form.data.statut} onValueChange={(value) => form.setData('statut', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner un statut" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="confirmée">Confirmée</SelectItem>
                    <SelectItem value="en attente">En attente</SelectItem>
                    <SelectItem value="annulée">Annulée</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="mb-1 block text-sm font-medium">Date de réservation</label>
                <Input type="date" value={form.data.date_reservation} onChange={(e) => form.setData('date_reservation', e.target.value)} />
              </div>
            </div>

            <div className="mt-4 border-t pt-4">
              <h3 className="mb-2 text-lg font-medium">Informations de paiement</h3>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="mb-1 block text-sm font-medium">Date de paiement</label>
                  <Input type="date" value={form.data.date_paiement} onChange={(e) => form.setData('date_paiement', e.target.value)} />
                </div>
                <div>
                  <label className="mb-1 block text-sm font-medium">Méthode de paiement</label>
                  <Input
                    value={form.data.methode_paiement}
                    onChange={(e) => form.setData('methode_paiement', e.target.value)}
                    placeholder="Ex: Carte bancaire, Espèces, Virement..."
                  />
                </div>
                <div>
                  <label className="mb-1 block text-sm font-medium">ID de transaction</label>
                  <Input
                    value={form.data.transaction_id}
                    onChange={(e) => form.setData('transaction_id', e.target.value)}
                    placeholder="Identifiant de la transaction"
                  />
                </div>
              </div>
            </div>

            <div className="mt-4 border-t pt-4">
              <h3 className="mb-2 text-lg font-medium">Informations sur l'infraction</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="mb-1 block text-sm font-medium">Date de l'infraction</label>
                  <Input type="date" value={form.data.date_infraction} onChange={(e) => form.setData('date_infraction', e.target.value)} />
                </div>
                <div>
                  <label className="mb-1 block text-sm font-medium">Heure de l'infraction</label>
                  <Input type="time" value={form.data.heure_infraction} onChange={(e) => form.setData('heure_infraction', e.target.value)} />
                </div>
              </div>
              <div className="mt-2">
                <label className="mb-1 block text-sm font-medium">Lieu de l'infraction</label>
                <Textarea value={form.data.lieu_infraction} onChange={(e) => form.setData('lieu_infraction', e.target.value)} rows={2} />
              </div>
            </div>

            <div className="mt-4 border-t pt-4">
              <h3 className="mb-2 text-lg font-medium">Documents</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="mb-1 block text-sm font-medium">Permis (recto)</label>
                  <div className="flex flex-col space-y-2">
                    {form.data.permis_recto && (
                      <div className="flex items-center space-x-2">
                        <a href={`/files/${form.data.permis_recto}`} target="_blank" className="flex items-center text-blue-600 hover:underline">
                          <svg xmlns="http://www.w3.org/2000/svg" className="mr-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                            />
                          </svg>
                          Télécharger le fichier existant
                        </a>
                      </div>
                    )}
                    <Input
                      id="permis_recto_file"
                      type="file"
                      onChange={(e) => {
                        if (e.target.files && e.target.files[0]) {
                          form.setData('permis_recto_file', e.target.files[0]);
                        }
                      }}
                    />
                    <Input
                      id="permis_recto"
                      value={form.data.permis_recto || ''}
                      onChange={(e) => form.setData('permis_recto', e.target.value)}
                      placeholder="Chemin du fichier (se remplit automatiquement après upload)"
                      className="mt-2"
                    />
                  </div>
                </div>
                <div>
                  <label className="mb-1 block text-sm font-medium">Permis (verso)</label>
                  <div className="flex flex-col space-y-2">
                    {form.data.permis_verso && (
                      <div className="flex items-center space-x-2">
                        <a href={`/files/${form.data.permis_verso}`} target="_blank" className="flex items-center text-blue-600 hover:underline">
                          <svg xmlns="http://www.w3.org/2000/svg" className="mr-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                            />
                          </svg>
                          Télécharger le fichier existant
                        </a>
                      </div>
                    )}
                    <Input
                      id="permis_verso_file"
                      type="file"
                      onChange={(e) => {
                        if (e.target.files && e.target.files[0]) {
                          form.setData('permis_verso_file', e.target.files[0]);
                        }
                      }}
                    />
                    <Input
                      id="permis_verso"
                      value={form.data.permis_verso || ''}
                      onChange={(e) => form.setData('permis_verso', e.target.value)}
                      placeholder="Chemin du fichier (se remplit automatiquement après upload)"
                      className="mt-2"
                    />
                  </div>
                </div>
              </div>
              <div className="mt-2 grid grid-cols-2 gap-4">
                <div>
                  <label className="mb-1 block text-sm font-medium">Lettre 48N (recto)</label>
                  <div className="flex flex-col space-y-2">
                    {form.data.lettre_48n_recto && (
                      <div className="flex items-center space-x-2">
                        <a href={`/files/${form.data.lettre_48n_recto}`} target="_blank" className="flex items-center text-blue-600 hover:underline">
                          <svg xmlns="http://www.w3.org/2000/svg" className="mr-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                            />
                          </svg>
                          Télécharger le fichier existant
                        </a>
                      </div>
                    )}
                    <Input
                      id="lettre_48n_recto_file"
                      type="file"
                      onChange={(e) => {
                        if (e.target.files && e.target.files[0]) {
                          form.setData('lettre_48n_recto_file', e.target.files[0]);
                        }
                      }}
                    />
                    <Input
                      id="lettre_48n_recto"
                      value={form.data.lettre_48n_recto || ''}
                      onChange={(e) => form.setData('lettre_48n_recto', e.target.value)}
                      placeholder="Chemin du fichier (se remplit automatiquement après upload)"
                      className="mt-2"
                    />
                  </div>
                </div>
                <div>
                  <label className="mb-1 block text-sm font-medium">Lettre 48N (verso)</label>
                  <div className="flex flex-col space-y-2">
                    {form.data.lettre_48n_verso && (
                      <div className="flex items-center space-x-2">
                        <a href={`/files/${form.data.lettre_48n_verso}`} target="_blank" className="flex items-center text-blue-600 hover:underline">
                          <svg xmlns="http://www.w3.org/2000/svg" className="mr-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                            />
                          </svg>
                          Télécharger le fichier existant
                        </a>
                      </div>
                    )}
                    <Input
                      id="lettre_48n_verso_file"
                      type="file"
                      onChange={(e) => {
                        if (e.target.files && e.target.files[0]) {
                          form.setData('lettre_48n_verso_file', e.target.files[0]);
                        }
                      }}
                    />
                    <Input
                      id="lettre_48n_verso"
                      value={form.data.lettre_48n_verso || ''}
                      onChange={(e) => form.setData('lettre_48n_verso', e.target.value)}
                      placeholder="Chemin du fichier (se remplit automatiquement après upload)"
                      className="mt-2"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
                Annuler
              </Button>
              <Button type="submit" disabled={form.processing}>
                {editingReservation ? 'Mettre à jour' : 'Ajouter'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      <Dialog open={isImportOpen} onOpenChange={setIsImportOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Importer des réservations</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleImportSubmit} className="space-y-4">
            <div>
              <Label htmlFor="file">Fichier Excel</Label>
              <Input id="file" type="file" accept=".xlsx,.xls" onChange={handleFileChange} className="mt-1" />
              {importForm.errors.file && <p className="mt-1 text-sm text-red-500">{importForm.errors.file}</p>}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              <p>Le fichier doit contenir les colonnes suivantes :</p>
              <ul className="mt-2 list-disc pl-5">
                <li>nom (obligatoire)</li>
                <li>prenom (obligatoire)</li>
                <li>email (obligatoire)</li>
                <li>stage_id (obligatoire)</li>
                <li>type_stage_id (obligatoire)</li>
                <li>date_reservation (optionnel, format: DD/MM/YYYY)</li>
                <li>statut (optionnel, valeurs: confirmée, en attente, annulée)</li>
                <li>methode_paiement (optionnel)</li>
                <li>civilite (optionnel)</li>
                <li>adresse (optionnel)</li>
                <li>cp (optionnel)</li>
                <li>ville (optionnel)</li>
                <li>date_naissance (optionnel, format: DD/MM/YYYY)</li>
                <li>lieu_naissance (optionnel)</li>
                <li>mobile (optionnel)</li>
                <li>tel (optionnel)</li>
                <li>num_permis (optionnel)</li>
                <li>date_permis (optionnel, format: DD/MM/YYYY)</li>
                <li>lieu_permis (optionnel)</li>
                <li>password (optionnel, si non fourni un mot de passe aléatoire sera généré)</li>
              </ul>
              <p className="mt-2">
                <strong>Note:</strong> Si l'utilisateur existe déjà (même email), la réservation sera associée à cet utilisateur.
              </p>
              <p className="mt-2">
                <strong>Note:</strong> Les dates peuvent être au format texte (JJ/MM/AAAA) ou au format numérique Excel.
              </p>
            </div>
            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={() => setIsImportOpen(false)}>
                Annuler
              </Button>
              <Button type="submit" disabled={importForm.processing || !importForm.data.file}>
                {importForm.processing ? 'Importation...' : 'Importer'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </AppLayout>
  );
}
